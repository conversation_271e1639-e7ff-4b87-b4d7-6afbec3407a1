'use client';

import { addSampleDataToLocalStorage, clearReceiptsFromLocalStorage } from '@/utils/sampleData';
import { useReceipts } from '@/context/ReceiptsContext';

/**
 * Development tools component for testing
 * This should only be used in development and removed in production
 */
export default function DevTools() {
  const { refreshReceipts } = useReceipts();

  const handleAddSampleData = () => {
    addSampleDataToLocalStorage();
    // Refresh the receipts context to show the new data
    refreshReceipts();
  };

  const handleClearData = () => {
    clearReceiptsFromLocalStorage();
    // Refresh the receipts context to show the cleared data
    refreshReceipts();
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
      <h3 className="text-sm font-medium text-yellow-800 mb-2">
        🛠️ Development Tools
      </h3>
      <p className="text-xs text-yellow-700 mb-3">
        These tools are only visible in development mode and help test the chart functionality.
      </p>
      <div className="flex gap-2">
        <button
          onClick={handleAddSampleData}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
        >
          Add Sample Data
        </button>
        <button
          onClick={handleClearData}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Clear All Data
        </button>
      </div>
    </div>
  );
}
