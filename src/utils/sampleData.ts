import { UploadedPdf } from "@/components/UploadedPdfsTable";

/**
 * Sample receipts data for testing the chart functionality
 * This creates receipts spread across the last 12 months
 */
export function generateSampleReceipts(): UploadedPdf[] {
  const sampleReceipts: UploadedPdf[] = [];
  const now = new Date();

  // Generate receipts for the last 24 months to test navigation
  for (let monthsAgo = 23; monthsAgo >= 0; monthsAgo--) {
    const receiptDate = new Date(
      now.getFullYear(),
      now.getMonth() - monthsAgo,
      Math.floor(Math.random() * 28) + 1
    );

    // Generate 1-4 receipts per month
    const receiptsThisMonth = Math.floor(Math.random() * 4) + 1;

    for (let i = 0; i < receiptsThisMonth; i++) {
      const dayOffset = Math.floor(Math.random() * 28);
      const receiptDateWithOffset = new Date(receiptDate);
      receiptDateWithOffset.setDate(receiptDate.getDate() + dayOffset);

      // Generate random receipt amounts between $5 and $150
      const totalCost = Math.round((Math.random() * 145 + 5) * 100) / 100;

      const stores = [
        "Mercadona",
        "Carrefour",
        "El Corte Inglés",
        "Lidl",
        "Alcampo",
        "Eroski",
      ];
      const storeName = stores[Math.floor(Math.random() * stores.length)];

      sampleReceipts.push({
        id: `sample-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 9)}`,
        fileName: `${storeName}_${
          receiptDateWithOffset.toISOString().split("T")[0]
        }.pdf`,
        date: receiptDateWithOffset,
        totalCost: totalCost,
      });
    }
  }

  return sampleReceipts.sort((a, b) => b.date.getTime() - a.date.getTime());
}

/**
 * Function to add sample data to localStorage for testing
 * This can be called from the browser console or used in development
 */
export function addSampleDataToLocalStorage(): void {
  if (typeof window === "undefined") {
    console.warn("This function can only be run in the browser");
    return;
  }

  const sampleReceipts = generateSampleReceipts();
  localStorage.setItem("uploadedPdfs", JSON.stringify(sampleReceipts));
  console.log(`Added ${sampleReceipts.length} sample receipts to localStorage`);
  console.log("Refresh the page to see the sample data");
}

/**
 * Function to clear all receipts from localStorage
 */
export function clearReceiptsFromLocalStorage(): void {
  if (typeof window === "undefined") {
    console.warn("This function can only be run in the browser");
    return;
  }

  localStorage.removeItem("uploadedPdfs");
  console.log("Cleared all receipts from localStorage");
  console.log("Refresh the page to see the changes");
}
