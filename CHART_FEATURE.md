# Monthly Spending Chart Feature

## Overview

The dashboard now includes a monthly spending chart that visualizes your receipt data over the last 12 months. This feature helps you track spending patterns and identify trends in your expenses.

## Features

- **Monthly Aggregation**: Shows total spending for each month over a 12-month window
- **Timeline Navigation**: Navigate through historical data with back/forward buttons (6-month increments)
- **Date Range Display**: Shows the current viewing period (e.g., "Jan 2023 - Dec 2023")
- **Interactive Chart**: Built with Recharts library for smooth interactions
- **Responsive Design**: Adapts to different screen sizes
- **Empty State**: Shows a helpful message when no data is available
- **Consistent Styling**: Matches the existing dashboard design

## Implementation Details

### Components

1. **MonthlySpendingChart** (`src/components/MonthlySpendingChart.tsx`)

   - Main chart component that takes receipts as props
   - Aggregates spending data by month
   - Renders a bar chart using Recharts
   - Handles empty states gracefully

2. **Dashboard Integration** (`src/app/dashboard/page.tsx`)
   - Chart is placed between the stats cards and recent receipts table
   - Uses the existing receipts data from the ReceiptsContext

### Data Processing

The chart component:

- Initializes a 12-month window with zero spending based on timeline offset
- Aggregates receipt data by month (YYYY-MM format) within the current window
- Converts data to chart-friendly format with month labels
- Rounds amounts to 2 decimal places for display

### Navigation System

- **Timeline Offset**: State variable tracking how many months back from current date
- **6-Month Increments**: Each navigation click moves the window by 6 months
- **Forward Limit**: Cannot navigate beyond the current month (offset = 0)
- **Backward Navigation**: No limit on how far back users can go
- **Date Range Display**: Automatically updates to show current viewing period

### Styling

- Uses Tailwind CSS for consistent styling
- Chart colors match the existing indigo theme (`#4f46e5`)
- Responsive container adapts to screen size
- Empty state includes helpful icon and messaging

## Development Tools

For testing purposes, development tools are included that allow you to:

- Add sample receipt data spanning 12 months
- Clear all receipt data
- These tools only appear in development mode

## Usage

1. **With Real Data**: Upload receipts through the existing upload functionality
2. **With Sample Data**: Use the development tools to add test data
3. **Chart Interaction**: Hover over bars to see exact amounts and months
4. **Timeline Navigation**:
   - Click the left arrow (←) to go back 6 months in time
   - Click the right arrow (→) to go forward 6 months (disabled when at current period)
   - The date range display shows the current viewing period

## Technical Notes

- Chart data is automatically updated when receipts are added/removed
- Uses the existing ReceiptsContext for data management
- Compatible with both localStorage and future Supabase implementations
- No additional dependencies required (Recharts was already installed)

## Future Enhancements

Potential improvements could include:

- Different chart types (line chart, pie chart)
- Date range selection
- Category-based spending breakdown
- Export functionality
- Comparison with previous periods
